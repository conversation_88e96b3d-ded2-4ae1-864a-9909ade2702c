#!/usr/bin/env python3
"""
简单的ASR测试脚本
"""
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_asr_import():
    """测试ASR模块导入"""
    print("开始测试ASR模块导入...")
    
    try:
        from handlers.asr.sensevoice.asr_handler_sensevoice import HandlerASR, ASRConfig
        print("✅ ASR处理器导入成功")
        
        # 创建ASR处理器
        asr_handler = HandlerASR()
        print("✅ ASR处理器创建成功")
        
        # 配置参数
        config = ASRConfig(
            model_name='iic/SenseVoiceSmall'
        )
        print("✅ ASR配置创建成功")
        
        # 加载处理器
        asr_handler.load(None, config)
        print("✅ ASR处理器加载成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_funasr_import():
    """测试funasr模块导入"""
    print("开始测试funasr模块导入...")
    
    try:
        import funasr
        print(f"✅ funasr导入成功，版本: {funasr.__version__}")
        
        from funasr import AutoModel
        print("✅ AutoModel导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ funasr导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== ASR功能测试 ===\n")
    
    # 测试funasr导入
    funasr_success = test_funasr_import()
    print()
    
    # 测试ASR处理器
    if funasr_success:
        asr_success = test_asr_import()
        print()
        
        if asr_success:
            print("🎉 ASR测试成功！")
        else:
            print("❌ ASR测试失败！")
    else:
        print("❌ funasr导入失败，跳过ASR测试！")
