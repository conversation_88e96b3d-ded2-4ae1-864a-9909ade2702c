{"name": "gradio_webrtc", "version": "0.11.0-beta.3", "description": "Gradio UI packages", "type": "module", "author": "", "license": "ISC", "private": false, "dependencies": {"@ffmpeg/ffmpeg": "^0.12.10", "@ffmpeg/util": "^0.12.1", "@gradio/atoms": "0.9.2", "@gradio/client": "1.7.0", "@gradio/icons": "0.8.0", "@gradio/image": "0.16.4", "@gradio/markdown": "^0.10.3", "@gradio/statustracker": "0.9.1", "@gradio/upload": "0.13.3", "@gradio/utils": "0.7.0", "@gradio/wasm": "0.14.2", "base64-js": "^1.5.1", "buffer": "^6.0.3", "eventemitter3": "^5.0.1", "gaussian-splat-renderer-for-lam": "^0.0.6", "hls.js": "^1.5.16", "mrmime": "^2.0.0", "p-queue": "^8.0.1", "python-struct": "^1.1.3"}, "devDependencies": {"@gradio/preview": "0.12.0", "less": "^4.2.2", "prettier": "^3.3.3", "prettier-plugin-svelte": "^3.3.3"}, "exports": {"./package.json": "./package.json", ".": {"gradio": "./index.ts", "svelte": "./dist/index.js", "types": "./dist/index.d.ts"}, "./example": {"gradio": "./Example.svelte", "svelte": "./dist/Example.svelte", "types": "./dist/Example.svelte.d.ts"}}, "peerDependencies": {"svelte": "^4.0.0"}, "main": "index.ts", "main_changeset": true, "repository": {"type": "git", "url": "git+https://github.com/gradio-app/gradio.git", "directory": "js/video"}}