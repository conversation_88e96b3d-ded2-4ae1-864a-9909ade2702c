import './style.css'
import IconFont from "./IconFont.svelte";
import Send from "./Send.svelte";
import Stop from "./Stop.svelte";
import CameraOff from "./CameraOff.svelte";
import CameraOn from "./CameraOn.svelte";
import VolumeOff from "./VolumeOff.svelte";
import VolumeOn from "./VolumeOn.svelte";
import SubtitleOn from "./SubtitleOn.svelte";
import SubtitleOff from "./SubtitleOff.svelte";
import MicOff from "./MicOff.svelte";
import MicOn from "./MicOn.svelte";
import Check from "./Check.svelte";
import PictureInPicture from "./PictureInPicture.svelte";
import SideBySide from "./SideBySide.svelte";

export {
  IconFont,
  CameraOff,
  CameraOn,
  VolumeOff,
  VolumeOn,
  SubtitleOn,
  SubtitleOff,
  <PERSON>c<PERSON>ff,
  <PERSON><PERSON><PERSON>n,
  Send,
  Check,
  PictureInPicture,
  SideBySide,
  Stop
}
