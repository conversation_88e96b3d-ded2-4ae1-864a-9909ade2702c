#!/usr/bin/env python3

import sys
import os

print("Python executable:", sys.executable)
print("Current working directory:", os.getcwd())
print("Python path:")
for i, path in enumerate(sys.path[:10]):
    print(f"  {i}: {path}")

try:
    import onnxruntime
    print(f"\n✅ onnxruntime imported successfully!")
    print(f"onnxruntime version: {onnxruntime.__version__}")
    print(f"onnxruntime location: {onnxruntime.__file__}")
except ImportError as e:
    print(f"\n❌ Failed to import onnxruntime: {e}")
except Exception as e:
    print(f"\n❌ Error importing onnxruntime: {e}")

try:
    import cv2
    print(f"\n✅ opencv-python imported successfully!")
    print(f"opencv version: {cv2.__version__}")
except ImportError as e:
    print(f"\n❌ Failed to import cv2: {e}")
except Exception as e:
    print(f"\n❌ Error importing cv2: {e}")
