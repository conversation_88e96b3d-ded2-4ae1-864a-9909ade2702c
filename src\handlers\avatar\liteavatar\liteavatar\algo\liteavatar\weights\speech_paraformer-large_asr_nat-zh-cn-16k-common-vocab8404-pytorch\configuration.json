{"framework": "pytorch", "task": "auto-speech-recognition", "model": {"type": "generic-asr", "am_model_name": "model.pb", "model_config": {"type": "pytorch", "code_base": "funasr", "mode": "paraformer", "lang": "zh-cn", "batch_size": 1, "am_model_config": "config.yaml", "lm_model_name": "lm/lm.pb", "lm_model_config": "lm/lm.yaml", "asr_model_config": "decoding.yaml", "mvn_file": "am.mvn", "model": "damo/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch"}}, "pipeline": {"type": "asr-inference"}}