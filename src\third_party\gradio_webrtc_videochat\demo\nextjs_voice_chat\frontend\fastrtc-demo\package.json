{"name": "fastrtc-demo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --no-lint", "start": "next start", "lint": "next lint"}, "dependencies": {"class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.4.10", "lucide-react": "^0.477.0", "next": "15.2.2-canary.1", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.2-canary.1", "tailwindcss": "^4", "typescript": "^5"}}