default:
  logger:
    log_level: "INFO"
  service:
    host: "0.0.0.0"
    port: 8282
    cert_file: "ssl_certs/localhost.crt"
    cert_key: "ssl_certs/localhost.key"
  chat_engine:
    model_root: "models"
    handler_search_path:
      - "src/handlers"
    handler_configs:
      RtcClient:
        module: client/rtc_client/client_handler_rtc
      SileroVad:
        module: vad/silerovad/vad_handler_silero
        speaking_threshold: 0.5
        start_delay: 2048
        end_delay: 5000
        buffer_look_back: 5000
        speech_padding: 512
      SenseVoice:
        enabled: True
        module: asr/sensevoice/asr_handler_sensevoice
        model_name: "iic/SenseVoiceSmall"
      Edge_TTS:
        enabled: True
        module: tts/edgetts/tts_handler_edgetts
        ref_audio_path: ""
        ref_audio_text: ""
        voice: "zh-CN-XiaoxiaoNeural"
        sample_rate: 24000
      # Simple Test LLM for testing - no API key needed
      SimpleTestLLM:
        enabled: True
        module: llm/simple_test/llm_handler_simple_test
        system_prompt: "我是一个简单的测试助手，专门用来测试语音对话功能。"
