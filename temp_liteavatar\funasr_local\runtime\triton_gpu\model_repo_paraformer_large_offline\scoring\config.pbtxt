# Copyright (c) 2023, NVIDIA CORPORATION.  All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: "scoring"
backend: "python"
max_batch_size: 64

parameters [
  {
    key: "ignore_id",
    value: { string_value: "-1"}
  },
  {
    key: "vocabulary",
    value: { string_value: "./model_repo_paraformer_large_offline/feature_extractor/config.yaml"}
  },
  {
    key: "lm_path"
    value: { string_value: "#lm_path"}
  },
  { key: "FORCE_CPU_ONLY_INPUT_TENSORS" 
    value: {string_value:"no"}
  }
]


input [
  {
    name: "logits"
    data_type: TYPE_FP32
    dims: [-1, 8404]
  },
  {
    name: "token_num"
    data_type: TYPE_INT32
    dims: [1]
    reshape: { shape: [ ] }
  }
]

output [
  {
    name: "OUTPUT0"
    data_type: TYPE_STRING
    dims: [1]
  }
]

dynamic_batching {
  }
instance_group [
    {
      count: 2
      kind: KIND_CPU
    }
  ]
