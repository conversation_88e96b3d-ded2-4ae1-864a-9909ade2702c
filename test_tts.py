#!/usr/bin/env python3
"""
简单的Edge TTS测试脚本
"""
import asyncio
import edge_tts
import io

async def test_edge_tts():
    """直接测试Edge TTS功能"""
    print("开始测试Edge TTS...")

    try:
        # 测试文本
        test_text = "你好，这是一个测试。"
        voice = "zh-CN-XiaoxiaoNeural"

        print(f"测试文本: {test_text}")
        print(f"使用语音: {voice}")

        # 创建TTS通信对象
        communicate = edge_tts.Communicate(test_text, voice)

        # 生成语音数据
        audio_data = b""
        async for chunk in communicate.stream():
            if chunk["type"] == "audio":
                audio_data += chunk["data"]

        if audio_data:
            print(f"✅ 语音生成成功，音频数据长度: {len(audio_data)} 字节")

            # 保存音频文件
            output_file = "test_output.mp3"
            with open(output_file, "wb") as f:
                f.write(audio_data)
            print(f"✅ 音频已保存到: {output_file}")

            return True
        else:
            print("❌ 语音生成失败")
            return False

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_edge_tts())
    if success:
        print("\n🎉 Edge TTS测试成功！")
    else:
        print("\n❌ Edge TTS测试失败！")
