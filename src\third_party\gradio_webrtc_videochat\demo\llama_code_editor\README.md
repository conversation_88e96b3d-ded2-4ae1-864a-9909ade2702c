---
title: Llama Code Editor
emoji: 🦙
colorFrom: indigo
colorTo: pink
sdk: gradio
sdk_version: 5.16.0
app_file: app.py
pinned: false
license: mit
short_description: Create interactive HTML web pages with your voice
tags: [webrtc, websocket, gradio, secret|TWILIO_ACCOUNT_SID, secret|TWILIO_AUTH_TOKEN,
secret|SAMBANOVA_API_KEY, secret|GROQ_API_KEY]
---

Check out the configuration reference at https://huggingface.co/docs/hub/spaces-config-reference
