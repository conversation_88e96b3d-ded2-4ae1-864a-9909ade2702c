// Copyright (c) 2021 Ximalaya Speech Team (Xiang <PERSON>)
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
syntax = "proto3";

option java_package = "ex.grpc";
option objc_class_prefix = "paraformer";

package paraformer;

service ASR {
  rpc Recognize (stream Request) returns (stream Response) {}
}

message Request {
  bytes audio_data = 1;
  string user = 2;
  string language = 3;
  bool speaking = 4;
  bool isEnd = 5;
}

message Response {
  string sentence = 1;
  string user = 2;
  string language = 3;
  string action = 4;
}
