# This file was autogenerated by uv via the following command:
#    uv export --format requirements-txt --no-hashes
aiofiles==23.2.1
aiohappyeyeballs==2.4.6
aiohttp==3.11.13
aiohttp-retry==2.9.1
aioice==0.9.0
aiortc==1.10.1
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.8.0
async-timeout==5.0.1 ; python_full_version < '3.11'
attrs==25.1.0
audioop-lts==0.2.1 ; python_full_version >= '3.13'
audioread==3.0.1
av==13.1.0
babel==2.17.0
beautifulsoup4==4.13.3
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
colorama==0.4.6
coloredlogs==15.0.1
colorlog==6.9.0
cryptography==44.0.1
csvw==3.5.1
decorator==5.2.1
dlinfo==2.0.0
dnspython==2.7.0
duckduckgo-search==7.5.0
espeakng-loader==0.2.4
exceptiongroup==1.2.2 ; python_full_version < '3.11'
fastapi==0.115.8
fastrtc==0.0.8.post1
fastrtc-moonshine-onnx==20241016
ffmpy==0.5.0
filelock==3.17.0
flatbuffers==25.2.10
frozenlist==1.5.0
fsspec==2025.2.0
google-crc32c==1.6.0
gradio==5.19.0
gradio-client==1.7.2
h11==0.14.0
httpcore==1.0.7
httpx==0.28.1
huggingface-hub==0.29.1
humanfriendly==10.0
idna==3.10
ifaddr==0.2.0
isodate==0.7.2
jinja2==3.1.5
joblib==1.4.2
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
kokoro-onnx==0.4.3
language-tags==1.2.0
lazy-loader==0.4
librosa==0.10.2.post1
llvmlite==0.44.0
lxml==5.3.1
markdown-it-py==3.0.0
markdownify==1.0.0
markupsafe==2.1.5
mdurl==0.1.2
mpmath==1.3.0
msgpack==1.1.0
multidict==6.1.0
numba==0.61.0
numpy==2.1.3
onnxruntime==1.20.1
orjson==3.10.15
packaging==24.2
pandas==2.2.3
phonemizer-fork==3.3.1
pillow==11.1.0
platformdirs==4.3.6
pooch==1.8.2
primp==0.14.0
propcache==0.3.0
protobuf==5.29.3
pycparser==2.22
pydantic==2.10.6
pydantic-core==2.27.2
pydub==0.25.1
pyee==12.1.1
pygments==2.19.1
pyjwt==2.10.1
pylibsrtp==0.11.0
pyopenssl==25.0.0
pyparsing==3.2.1
pyreadline3==3.5.4 ; sys_platform == 'win32'
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-multipart==0.0.20
pytz==2025.1
pyyaml==6.0.2
rdflib==7.1.3
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
rfc3986==1.5.0
rich==13.9.4
rpds-py==0.23.1
ruff==0.9.7 ; sys_platform != 'emscripten'
safehttpx==0.1.6
scikit-learn==1.6.1
scipy==1.15.2
segments==2.3.0
semantic-version==2.10.0
shellingham==1.5.4 ; sys_platform != 'emscripten'
six==1.17.0
smolagents==1.9.2
sniffio==1.3.1
soundfile==0.13.1
soupsieve==2.6
soxr==0.5.0.post1
standard-aifc==3.13.0 ; python_full_version >= '3.13'
standard-chunk==3.13.0 ; python_full_version >= '3.13'
standard-sunau==3.13.0 ; python_full_version >= '3.13'
starlette==0.45.3
sympy==1.13.3
threadpoolctl==3.5.0
tokenizers==0.21.0
tomlkit==0.13.2
tqdm==4.67.1
twilio==9.4.6
typer==0.15.1 ; sys_platform != 'emscripten'
typing-extensions==4.12.2
tzdata==2025.1
uritemplate==4.1.1
urllib3==2.3.0
uvicorn==0.34.0 ; sys_platform != 'emscripten'
websockets==15.0
yarl==1.18.3
